import { <PERSON><PERSON><PERSON>, Column, PrimaryColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

@Entity('generic_page_templates')
export class GenericPageTemplate {
  @PrimaryColumn({ type: 'varchar', length: 36 })
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Index('idx_generic_page_templates_category')
  category?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  thumbnail?: string;

  @Column({ type: 'jsonb' })
  config: Record<string, any>;

  @Column({ type: 'bigint' })
  created_at: number;

  @Column({ type: 'bigint' })
  updated_at: number;

  @Column({ type: 'varchar', length: 36, nullable: true })
  created_by?: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  updated_by?: string;
}