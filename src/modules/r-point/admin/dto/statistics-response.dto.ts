import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thống kê theo trạng thái giao dịch
 */
export class TransactionStatusStatDto {
  @ApiProperty({
    description: 'Trạng thái giao dịch',
    example: 'CONFIRMED'
  })
  status: string;

  @ApiProperty({
    description: 'Số lượng giao dịch',
    example: 100
  })
  count: number;

  @ApiProperty({
    description: 'Tổng số tiền',
    example: 10000000
  })
  totalAmount: number;
}

/**
 * DTO cho thống kê theo phương thức thanh toán
 */
export class PaymentMethodStatDto {
  @ApiProperty({
    description: 'Phương thức thanh toán',
    example: 'VNPAY'
  })
  paymentMethod: string;

  @ApiProperty({
    description: 'Số lượng giao dịch',
    example: 80
  })
  count: number;

  @ApiProperty({
    description: 'Tổng số tiền',
    example: 8000000
  })
  totalAmount: number;
}

/**
 * DTO cho thống kê theo gói point
 */
export class PointPackageStatDto {
  @ApiProperty({
    description: 'ID của gói point',
    example: 1
  })
  pointId: number;

  @ApiProperty({
    description: 'Tên của gói point',
    example: 'Gói 100k'
  })
  pointName: string;

  @ApiProperty({
    description: 'Số lượng giao dịch',
    example: 50
  })
  count: number;

  @ApiProperty({
    description: 'Tổng số tiền',
    example: 5000000
  })
  totalAmount: number;

  @ApiProperty({
    description: 'Tổng số point',
    example: 5000
  })
  totalPoints: number;
}

/**
 * DTO cho thống kê theo coupon
 */
export class CouponStatDto {
  @ApiProperty({
    description: 'ID của coupon',
    example: '550e8400-e29b-41d4-a716-446655440000'
  })
  couponId: string;

  @ApiProperty({
    description: 'Mã coupon',
    example: 'SUMMER2023'
  })
  couponCode: string;

  @ApiProperty({
    description: 'Số lượng sử dụng',
    example: 30
  })
  usageCount: number;

  @ApiProperty({
    description: 'Tổng số tiền giảm giá',
    example: 300000
  })
  totalDiscountAmount: number;
}

/**
 * DTO cho response trả về thống kê tổng quan
 */
export class StatisticsOverviewDto {
  @ApiProperty({
    description: 'Tổng số giao dịch',
    example: 200
  })
  totalTransactions: number;

  @ApiProperty({
    description: 'Tổng số tiền giao dịch',
    example: 20000000
  })
  totalAmount: number;

  @ApiProperty({
    description: 'Tổng số point đã bán',
    example: 20000
  })
  totalPointsSold: number;

  @ApiProperty({
    description: 'Số lượng người dùng đã mua point',
    example: 150
  })
  uniqueUsers: number;

  @ApiProperty({
    description: 'Giá trị giao dịch trung bình',
    example: 100000
  })
  averageTransactionValue: number;
}

/**
 * DTO cho response trả về thống kê chi tiết
 */
export class StatisticsDetailDto {
  @ApiProperty({
    description: 'Thống kê theo trạng thái giao dịch',
    type: [TransactionStatusStatDto]
  })
  byStatus: TransactionStatusStatDto[];

  @ApiProperty({
    description: 'Thống kê theo phương thức thanh toán',
    type: [PaymentMethodStatDto]
  })
  byPaymentMethod: PaymentMethodStatDto[];

  @ApiProperty({
    description: 'Thống kê theo gói point',
    type: [PointPackageStatDto]
  })
  byPointPackage: PointPackageStatDto[];

  @ApiProperty({
    description: 'Thống kê theo coupon',
    type: [CouponStatDto]
  })
  byCoupon: CouponStatDto[];
}

/**
 * DTO cho response trả về thống kê
 */
export class StatisticsResponseDto {
  @ApiProperty({
    description: 'Thống kê tổng quan',
    type: StatisticsOverviewDto
  })
  overview: StatisticsOverviewDto;

  @ApiProperty({
    description: 'Thống kê chi tiết',
    type: StatisticsDetailDto
  })
  details: StatisticsDetailDto;
}
