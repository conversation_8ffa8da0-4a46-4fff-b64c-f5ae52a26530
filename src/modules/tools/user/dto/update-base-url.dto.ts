import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUrl, IsUUID } from 'class-validator';

/**
 * DTO cho việc cập nhật base URL của công cụ tùy chỉnh
 */
export class UpdateBaseUrlDto {
  /**
   * ID của công cụ tùy chỉnh cần cập nhật
   */
  @ApiProperty({
    description: 'ID của công cụ tùy chỉnh cần cập nhật',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsNotEmpty()
  toolId: string;

  /**
   * Base URL mới cho công cụ tùy chỉnh
   */
  @ApiProperty({
    description: 'Base URL mới cho công cụ tùy chỉnh',
    example: 'https://api.example.com'
  })
  @IsUrl({
    require_protocol: true,
    require_valid_protocol: true,
    protocols: ['http', 'https']
  }, {
    message: 'Base URL phải là một URL hợp lệ bắt đầu bằng http:// hoặc https://'
  })
  @IsString()
  @IsNotEmpty()
  baseUrl: string;
}
