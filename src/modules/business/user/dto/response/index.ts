// Export all response DTOs
export * from '../base/base-product-response.dto';
export * from './physical-product-response.dto';
export * from './digital-product-response.dto';
export * from './event-product-response.dto';
export * from './service-product-response.dto';
export * from './combo-product-response.dto';

// Import all response DTOs for union types
import { PhysicalProductResponseDto } from './physical-product-response.dto';
import { DigitalProductResponseDto } from './digital-product-response.dto';
import { EventProductResponseDto } from './event-product-response.dto';
import { ServiceProductResponseDto } from './service-product-response.dto';
import { ComboProductResponseDto } from './combo-product-response.dto';
import { ProductTypeEnum } from '@modules/business/enums';

/**
 * Union type cho tất cả các loại sản phẩm response DTOs
 */
export type ProductResponseDto = 
  | PhysicalProductResponseDto
  | DigitalProductResponseDto
  | EventProductResponseDto
  | ServiceProductResponseDto
  | ComboProductResponseDto;

/**
 * Type guard functions cho response DTOs
 */
export function isPhysicalProductResponse(dto: ProductResponseDto): dto is PhysicalProductResponseDto {
  return dto.productType === ProductTypeEnum.PHYSICAL;
}

export function isDigitalProductResponse(dto: ProductResponseDto): dto is DigitalProductResponseDto {
  return dto.productType === ProductTypeEnum.DIGITAL;
}

export function isEventProductResponse(dto: ProductResponseDto): dto is EventProductResponseDto {
  return dto.productType === ProductTypeEnum.EVENT;
}

export function isServiceProductResponse(dto: ProductResponseDto): dto is ServiceProductResponseDto {
  return dto.productType === ProductTypeEnum.SERVICE;
}

export function isComboProductResponse(dto: ProductResponseDto): dto is ComboProductResponseDto {
  return dto.productType === ProductTypeEnum.COMBO;
}

/**
 * Function để lấy loại response DTO phù hợp
 */
export function getResponseDtoType(productType: ProductTypeEnum): string {
  switch (productType) {
    case ProductTypeEnum.PHYSICAL:
      return 'PhysicalProductResponseDto';
    case ProductTypeEnum.DIGITAL:
      return 'DigitalProductResponseDto';
    case ProductTypeEnum.EVENT:
      return 'EventProductResponseDto';
    case ProductTypeEnum.SERVICE:
      return 'ServiceProductResponseDto';
    case ProductTypeEnum.COMBO:
      return 'ComboProductResponseDto';
    default:
      throw new Error(`Unknown product type: ${productType}`);
  }
}

/**
 * Function để validate response DTO theo loại sản phẩm
 */
export function validateResponseDto(dto: ProductResponseDto): boolean {
  switch (dto.productType) {
    case ProductTypeEnum.PHYSICAL:
      return isPhysicalProductResponse(dto);
    case ProductTypeEnum.DIGITAL:
      return isDigitalProductResponse(dto) && !!dto.advancedInfo;
    case ProductTypeEnum.EVENT:
      return isEventProductResponse(dto) && !!dto.advancedInfo;
    case ProductTypeEnum.SERVICE:
      return isServiceProductResponse(dto) && !!dto.advancedInfo;
    case ProductTypeEnum.COMBO:
      return isComboProductResponse(dto) && !!dto.advancedInfo;
    default:
      return false;
  }
}
